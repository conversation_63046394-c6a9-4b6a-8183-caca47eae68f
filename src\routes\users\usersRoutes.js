import { Router } from 'express'
import asyncHandler from '../../common/handlers/asyncHandler'
import {
  createUser,
  deleteUser,
  readAllUser,
  readUser,
  updateUser
} from '../../controller/usersController'
import { isAuthenticated } from '../../middleware/authMiddleware'

const router = Router()

// Note: requestMiddleware removed due to MongoDB model dependency cleanup
router.get('/', isAuthenticated, asyncHandler(readAllUser))
router.post('/', asyncHandler(createUser))
router.get('/:userID', asyncHandler(readUser))
router.put('/:userID', asyncHandler(updateUser))
router.delete('/:userID', asyncHandler(deleteUser))

export default router
