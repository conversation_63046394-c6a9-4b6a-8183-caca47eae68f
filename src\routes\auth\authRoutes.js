import { Router } from 'express'
import asyncHandler from '../../common/handlers/asyncHandler'
import { register, login, logout } from '../../controller/auth/authController'
import limiter from '../../middleware/requestLimiterMiddleware'

const router = Router()

router.post('/login', asyncHandler(login))
router.post('/register', limiter, asyncHandler(register))
router.post('/:logout', asyncHandler(logout))

export default router
