import User, { Password, Role } from '../models/User'

export const createUser = async (req, res, next) => {
  try {
    const { firstName, lastName, email, password, roles } = req.body

    /**
     * This guard is necessary to prevent saving of the
     * password while the user is not saved due to a missing required field.
     */
    if (firstName === undefined || firstName === null) {
      res.response(
        {
          errors: {
            message: ["The field 'firstName' is required"]
          }
        },
        400
      )
      return
    }

    const existingUser = await User.findOne({ email })
    if (existingUser != null) {
      /**
       * TODO: This is for security reasons to preserve our user's data
       * Need to have a better approach for this
       */
      res.response(
        { errors: { message: ["The field 'email' is invalid"] } },
        400
      )
      return
    }

    const user = new User({
      firstName,
      lastName,
      email
    })

    const userPassword = new Password({
      hash: password,
      user: user._id
    })

    // Find a suitable roles
    if (!roles || roles.lenth === 0) {
      /**
       * Apply a default role if no role is given.
       * But we need to look for the default role, otherwise we create it
       */
      const defaultRole = await Role.findOne({ name: 'user' })
      if (defaultRole == null) {
        const newRole = new Role({
          name: 'user',
          permissions: ['read']
        })

        user.roles = [newRole._id]
        await newRole.save()
      } else {
        user.roles = [defaultRole._id]
      }
    } else {
      /**
       * Assuming payload.roles is an array of ObjectId from the client app
       */
      user.roles = [...roles]
    }

    user.password = userPassword._id

    await userPassword.save()
    await user.save()

    res.success(`User with email ${email} created`, 201)
    return
  } catch (err) {
    next(err)
  }
}
export const readUser = async (req, res, next) => {
  try {
    const { userID } = req.params

    const user = await User.findById(userID)

    res.success(user)
    return
  } catch (err) {
    next(err)
  }
}
export const readAllUser = async (req, res, next) => {
  res.success(res.data)
}
export const updateUser = async (req, res, next) => {
  try {
    const { userID } = req.params
    const payload = req.body

    const user = await User.findById(userID).select('-password') // Do not include the password field

    if (user) {
      /**
       * TODO: SECURITY RISK. We should implement a reset password instead.
       *
       * If we see a password in the params, update the password as well
       */
      if (payload.password) {
        const userPassword = await Password.findOne({ user: user._id })
        // If we know that this user actually has an old password, we update it.
        if (userPassword != null) {
          userPassword.hash = payload.password

          // Remove the password field in the payload
          delete payload.password

          // Update the user password
          await userPassword.save()
        }
      }

      user.set(payload)
      await user.save()

      res.response({
        message: 'User is successfully updates',
        data: user
      })
      return
    }

    res.notFound()
  } catch (err) {
    next(err)
  }
}
export const deleteUser = async (req, res, next) => {
  try {
    const { userID } = req.params

    const user = await User.findById(userID)
    if (!user) {
      res.notFound('User not found')
      return
    }

    await user.remove()

    res.success('User deleted succesfully!')
  } catch (err) {
    next(err)
  }
}
