import jwt from 'jsonwebtoken'
import { env } from '../utils/env'
import { Role } from '../models/User'

export const authorize = (permissions) => {
  return async (req, res, next) => {
    const user = req.user

    try {
      if (user !== undefined) {
        const userRoles = await Role.find({
          _id: { $in: user.roles || [] }
        })

        const hasPermission = permissions.every((permission) => {
          return userRoles.some((role) => role.name === permission)
        })

        if (!hasPermission) {
          res.forbidden()
          return
        }

        next()
      }
    } catch (err) {
      res.internalError(err.message)
    }
  }
}

export const isAuthenticated = (req, res, next) => {
  try {
    const token = req.headers.authorization?.split(' ')[1]

    const decoded = jwt.verify(token, env('JWT_SECRET_KEY'))

    req.user = decoded
    next()
  } catch (err) {
    next(err)
  }
}
